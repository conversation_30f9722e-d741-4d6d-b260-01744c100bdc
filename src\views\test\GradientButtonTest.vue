<template>
  <div class="gradient-button-test">
    <div class="header">
      <h1>🎨 GradientButton 组件测试</h1>
      <p>可配置渐变色按钮的完整演示和测试</p>
    </div>

    <!-- 控制面板 -->
    <div class="control-panel">
      <h2>🎛️ 实时控制面板</h2>
      <div class="controls">
        <div class="control-group">
          <label>按钮文本:</label>
          <input v-model="customText" placeholder="输入按钮文本" />
        </div>

        <div class="control-group">
          <label>背景渐变:</label>
          <input v-model="customBackgroundGradient" placeholder="linear-gradient(...)" />
        </div>

        <div class="control-group">
          <label>边框渐变:</label>
          <input v-model="customBorderGradient" placeholder="linear-gradient(...)" />
        </div>

        <div class="control-group">
          <label>禁用渐变:</label>
          <input v-model="customDisabledGradient" placeholder="linear-gradient(...)" />
        </div>

        <div class="control-group">
          <label>状态:</label>
          <div class="checkbox-group">
            <label><input type="checkbox" v-model="isDisabled" /> 禁用</label>
            <label><input type="checkbox" v-model="isLoading" /> 加载中</label>
          </div>
        </div>

        <div class="control-group">
          <button @click="resetCustom" class="reset-btn">重置</button>
          <button @click="randomizeColors" class="random-btn">随机颜色</button>
        </div>
      </div>

      <!-- 自定义按钮预览 -->
      <div class="custom-preview">
        <h3>自定义按钮预览</h3>
        <GradientButton
          :text="customText"
          :background-gradient="customBackgroundGradient"
          :border-gradient="customBorderGradient"
          :disabled-gradient="customDisabledGradient"
          :disabled="isDisabled"
          :loading="isLoading"
          @click="handleCustomClick"
        />
      </div>
    </div>

    <!-- 预设主题演示 -->
    <div class="demo-section">
      <h2>🎨 预设主题演示</h2>
      <div class="theme-grid">
        <!-- 默认样式 -->
        <div class="theme-card">
          <h3>默认样式</h3>
          <GradientButton @click="handleClick">默认按钮</GradientButton>
        </div>

        <!-- 蓝色主题 -->
        <div class="theme-card">
          <h3>蓝色主题</h3>
          <GradientButton
            text="蓝色按钮"
            background-gradient="linear-gradient(90deg, #4facfe 0%, #00f2fe 100%)"
            border-gradient="linear-gradient(85deg, #e0f4ff 0%, #b3e5fc 50%, #81d4fa 100%)"
            @click="handleClick"
          />
        </div>

        <!-- 紫色主题 -->
        <div class="theme-card">
          <h3>紫色主题</h3>
          <GradientButton
            text="紫色按钮"
            background-gradient="linear-gradient(90deg, #a855f7 0%, #8b5cf6 100%)"
            border-gradient="linear-gradient(85deg, #f3e8ff 0%, #e9d5ff 50%, #ddd6fe 100%)"
            @click="handleClick"
          />
        </div>

        <!-- 绿色主题 -->
        <div class="theme-card">
          <h3>绿色主题</h3>
          <GradientButton
            text="绿色按钮"
            background-gradient="linear-gradient(90deg, #10b981 0%, #059669 100%)"
            border-gradient="linear-gradient(85deg, #d1fae5 0%, #a7f3d0 50%, #6ee7b7 100%)"
            @click="handleClick"
          />
        </div>

        <!-- 红色主题 -->
        <div class="theme-card">
          <h3>红色主题</h3>
          <GradientButton
            text="红色按钮"
            background-gradient="linear-gradient(90deg, #ef4444 0%, #dc2626 100%)"
            border-gradient="linear-gradient(85deg, #fee2e2 0%, #fecaca 50%, #fca5a5 100%)"
            @click="handleClick"
          />
        </div>

        <!-- 橙色主题 -->
        <div class="theme-card">
          <h3>橙色主题</h3>
          <GradientButton
            text="橙色按钮"
            background-gradient="linear-gradient(90deg, #f97316 0%, #ea580c 100%)"
            border-gradient="linear-gradient(85deg, #fed7aa 0%, #fdba74 50%, #fb923c 100%)"
            @click="handleClick"
          />
        </div>
      </div>
    </div>

    <!-- 状态演示 -->
    <div class="demo-section">
      <h2>⚙️ 状态演示</h2>
      <div class="state-grid">
        <div class="state-card">
          <h3>正常状态</h3>
          <GradientButton
            text="正常按钮"
            background-gradient="linear-gradient(90deg, #4facfe 0%, #00f2fe 100%)"
            @click="handleClick"
          />
        </div>

        <div class="state-card">
          <h3>禁用状态</h3>
          <GradientButton
            text="禁用按钮"
            background-gradient="linear-gradient(90deg, #4facfe 0%, #00f2fe 100%)"
            disabled
            @click="handleClick"
          />
        </div>

        <div class="state-card">
          <h3>加载状态</h3>
          <GradientButton
            text="加载中..."
            background-gradient="linear-gradient(90deg, #a855f7 0%, #8b5cf6 100%)"
            :loading="demoLoading"
            @click="toggleDemoLoading"
          />
        </div>
      </div>
    </div>

    <!-- 渐变方向演示 -->
    <div class="demo-section">
      <h2>📐 渐变方向演示</h2>
      <div class="direction-grid">
        <div class="direction-card">
          <h3>水平渐变 (90deg)</h3>
          <GradientButton
            text="水平渐变"
            background-gradient="linear-gradient(90deg, #ff6b6b 0%, #feca57 100%)"
            border-gradient="linear-gradient(90deg, #ffe8e8 0%, #fff4e6 100%)"
            @click="handleClick"
          />
        </div>

        <div class="direction-card">
          <h3>垂直渐变 (180deg)</h3>
          <GradientButton
            text="垂直渐变"
            background-gradient="linear-gradient(180deg, #ff6b6b 0%, #feca57 100%)"
            border-gradient="linear-gradient(180deg, #ffe8e8 0%, #fff4e6 100%)"
            @click="handleClick"
          />
        </div>

        <div class="direction-card">
          <h3>对角渐变 (45deg)</h3>
          <GradientButton
            text="对角渐变"
            background-gradient="linear-gradient(45deg, #ff6b6b 0%, #feca57 100%)"
            border-gradient="linear-gradient(45deg, #ffe8e8 0%, #fff4e6 100%)"
            @click="handleClick"
          />
        </div>

        <div class="direction-card">
          <h3>径向渐变</h3>
          <GradientButton
            text="径向渐变"
            background-gradient="radial-gradient(circle, #667eea 0%, #764ba2 100%)"
            border-gradient="linear-gradient(45deg, #e8f0fe 0%, #f3e8ff 100%)"
            @click="handleClick"
          />
        </div>
      </div>
    </div>

    <!-- 事件日志 -->
    <div class="event-log">
      <h2>📋 事件日志</h2>
      <div class="log-content">
        <div v-for="(log, index) in eventLogs" :key="index" class="log-item">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-event">{{ log.event }}</span>
          <span class="log-data">{{ log.data }}</span>
        </div>
      </div>
      <button @click="clearLogs" class="clear-btn">清空日志</button>
    </div>

    <!-- 返回按钮 -->
    <div class="footer">
      <button @click="goBack" class="back-btn">返回测试导航</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useRouter } from "vue-router";
import GradientButton from "@/components/GradientButton/index.vue";

const router = useRouter();

// 自定义控制
const customText = ref("自定义按钮");
const customBackgroundGradient = ref("linear-gradient(90deg, #ffbd55 0%, #ff572a 100%)");
const customBorderGradient = ref("linear-gradient(85deg, #fff0bf 0%, #ffe1c3 50%, #ffd3c8 100%)");
const customDisabledGradient = ref("linear-gradient(135deg, #d3d3d3 0%, #a9a9a9 100%)");
const isDisabled = ref(false);
const isLoading = ref(false);

// 演示状态
const demoLoading = ref(false);

// 事件日志
const eventLogs = ref<Array<{ time: string; event: string; data: string }>>([]);

// 添加日志
const addLog = (event: string, data: any = "") => {
  const time = new Date().toLocaleTimeString();
  eventLogs.value.unshift({
    time,
    event,
    data: typeof data === "object" ? JSON.stringify(data) : String(data),
  });

  // 限制日志数量
  if (eventLogs.value.length > 30) {
    eventLogs.value = eventLogs.value.slice(0, 30);
  }
};

// 事件处理
const handleClick = () => {
  addLog("按钮点击", "普通按钮被点击");
};

const handleCustomClick = () => {
  addLog("自定义按钮点击", `文本: ${customText.value}`);
};

const toggleDemoLoading = () => {
  demoLoading.value = !demoLoading.value;
  addLog("切换加载状态", `loading: ${demoLoading.value}`);
  if (demoLoading.value) {
    setTimeout(() => {
      demoLoading.value = false;
      addLog("加载完成", "自动停止加载");
    }, 3000);
  }
};

// 控制函数
const resetCustom = () => {
  customText.value = "自定义按钮";
  customBackgroundGradient.value = "linear-gradient(90deg, #ffbd55 0%, #ff572a 100%)";
  customBorderGradient.value = "linear-gradient(85deg, #fff0bf 0%, #ffe1c3 50%, #ffd3c8 100%)";
  customDisabledGradient.value = "linear-gradient(135deg, #d3d3d3 0%, #a9a9a9 100%)";
  isDisabled.value = false;
  isLoading.value = false;
  addLog("重置自定义", "恢复默认设置");
};

const randomizeColors = () => {
  const colors = [
    "#ff6b6b",
    "#4ecdc4",
    "#45b7d1",
    "#96ceb4",
    "#feca57",
    "#ff9ff3",
    "#54a0ff",
    "#5f27cd",
    "#00d2d3",
    "#ff9f43",
    "#a55eea",
    "#26de81",
    "#fd79a8",
    "#fdcb6e",
    "#6c5ce7",
  ];

  const getRandomColor = () => colors[Math.floor(Math.random() * colors.length)];
  const getRandomAngle = () => Math.floor(Math.random() * 360);

  const angle = getRandomAngle();
  const color1 = getRandomColor();
  const color2 = getRandomColor();

  customBackgroundGradient.value = `linear-gradient(${angle}deg, ${color1} 0%, ${color2} 100%)`;
  customBorderGradient.value = `linear-gradient(${angle}deg, ${color1}33 0%, ${color2}33 100%)`;

  addLog("随机颜色", `角度: ${angle}deg, 颜色: ${color1} -> ${color2}`);
};

const clearLogs = () => {
  eventLogs.value = [];
  addLog("清空日志", "日志已清空");
};

const goBack = () => {
  router.push("/test");
};
</script>

<style lang="scss" scoped>
.gradient-button-test {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
  font-family: "Inter", sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 40px;

  h1 {
    color: #2d3748;
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  p {
    color: #4a5568;
    font-size: 1.1rem;
  }
}

.control-panel {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 30px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);

  h2 {
    color: #2d3748;
    margin-bottom: 20px;
    font-size: 1.5rem;
  }

  .controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
  }

  .control-group {
    display: flex;
    flex-direction: column;
    gap: 8px;

    label {
      font-weight: 500;
      color: #4a5568;
    }

    input {
      padding: 8px 12px;
      border: 1px solid #e2e8f0;
      border-radius: 6px;
      font-size: 14px;

      &:focus {
        outline: none;
        border-color: #4299e1;
        box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
      }
    }

    .checkbox-group {
      display: flex;
      gap: 16px;

      label {
        display: flex;
        align-items: center;
        gap: 6px;
        font-weight: normal;
      }
    }

    .reset-btn,
    .random-btn {
      padding: 8px 16px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.2s;
    }

    .reset-btn {
      background: #e53e3e;
      color: white;

      &:hover {
        background: #c53030;
      }
    }

    .random-btn {
      background: #9f7aea;
      color: white;

      &:hover {
        background: #805ad5;
      }
    }
  }

  .custom-preview {
    text-align: center;
    padding: 20px;
    background: #f7fafc;
    border-radius: 8px;

    h3 {
      color: #4a5568;
      margin-bottom: 16px;
    }
  }
}

.demo-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 30px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);

  h2 {
    color: #2d3748;
    margin-bottom: 20px;
    font-size: 1.5rem;
  }
}

.theme-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.theme-card {
  text-align: center;
  padding: 20px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: #f7fafc;

  h3 {
    color: #4a5568;
    margin-bottom: 16px;
    font-size: 1rem;
  }
}

.state-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.state-card {
  text-align: center;
  padding: 20px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: #f7fafc;

  h3 {
    color: #4a5568;
    margin-bottom: 16px;
    font-size: 1rem;
  }
}

.direction-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.direction-card {
  text-align: center;
  padding: 20px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: #f7fafc;

  h3 {
    color: #4a5568;
    margin-bottom: 16px;
    font-size: 0.9rem;
  }
}

.event-log {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 30px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);

  h2 {
    color: #2d3748;
    margin-bottom: 20px;
    font-size: 1.5rem;
  }

  .log-content {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 12px;
    background: #f7fafc;
    margin-bottom: 16px;

    .log-item {
      display: flex;
      gap: 12px;
      padding: 6px 0;
      border-bottom: 1px solid #e2e8f0;
      font-size: 13px;

      &:last-child {
        border-bottom: none;
      }

      .log-time {
        color: #718096;
        min-width: 80px;
      }

      .log-event {
        color: #2d3748;
        min-width: 120px;
        font-weight: 500;
      }

      .log-data {
        color: #4a5568;
        flex: 1;
      }
    }
  }

  .clear-btn {
    background: #e53e3e;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;

    &:hover {
      background: #c53030;
    }
  }
}

.footer {
  text-align: center;
  margin-top: 40px;

  .back-btn {
    background: #4299e1;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.2s;

    &:hover {
      background: #3182ce;
    }
  }
}

@media (max-width: 768px) {
  .controls {
    grid-template-columns: 1fr;
  }

  .theme-grid,
  .state-grid,
  .direction-grid {
    grid-template-columns: 1fr;
  }

  .header h1 {
    font-size: 2rem;
  }
}
</style>
