<template>
  <div class="test-index">
    <div class="header">
      <h1>🧪 测试页面导航</h1>
      <p>开发和测试组件的页面集合</p>
    </div>

    <div class="test-categories">
      <!-- 组件测试 -->
      <div class="category">
        <h2>🎨 组件测试</h2>
        <div class="test-grid">
          <div class="test-card" @click="navigateTo('/test/public-activity-bonus-tip')">
            <div class="card-icon">🎁</div>
            <h3>通用奖励弹窗</h3>
            <p>PublicActivityBonusTip 组件测试</p>
            <div class="card-tags">
              <span class="tag">弹窗</span>
              <span class="tag">奖励</span>
              <span class="tag">动画</span>
            </div>
          </div>

          <div class="test-card" @click="navigateTo('/test/coin')">
            <div class="card-icon">🪙</div>
            <h3>金币动画</h3>
            <p>CoinAnimation 组件演示</p>
            <div class="card-tags">
              <span class="tag">动画</span>
              <span class="tag">金币</span>
            </div>
          </div>

          <div class="test-card" @click="navigateTo('/test/wallet-reward-btn')">
            <div class="card-icon">💰</div>
            <h3>钱包奖励按钮</h3>
            <p>WalletRewardBtn 图片序列动画测试</p>
            <div class="card-tags">
              <span class="tag">按钮</span>
              <span class="tag">序列动画</span>
            </div>
          </div>

          <div class="test-card" @click="navigateTo('/test/gradient-button')">
            <div class="card-icon">🎨</div>
            <h3>渐变按钮测试</h3>
            <p>GradientButton 组件完整演示</p>
            <div class="card-tags">
              <span class="tag">按钮</span>
              <span class="tag">渐变</span>
              <span class="tag">主题</span>
            </div>
          </div>

          <div class="test-card" @click="navigateTo('/test/copylink-test')">
            <div class="card-icon">🔗</div>
            <h3>复制链接测试</h3>
            <p>CopyLink 功能测试</p>
            <div class="card-tags">
              <span class="tag">复制</span>
              <span class="tag">链接</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 功能测试 -->
      <div class="category">
        <h2>⚙️ 功能测试</h2>
        <div class="test-grid">
          <div class="test-card" @click="navigateTo('/test/bonus-wallet-test')">
            <div class="card-icon">🎯</div>
            <h3>奖励钱包返回逻辑</h3>
            <p>BonusWallet 返回逻辑测试</p>
            <div class="card-tags">
              <span class="tag">逻辑</span>
              <span class="tag">钱包</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="footer">
      <p>💡 提示：这些测试页面用于开发和调试组件功能</p>
      <button @click="goBack" class="back-btn">返回首页</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from "vue-router";

const router = useRouter();

const navigateTo = (path: string) => {
  router.push(path);
};

const goBack = () => {
  router.push("/home");
};
</script>

<style lang="scss" scoped>
.test-index {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  font-family: "Inter", sans-serif;
}

.header {
  text-align: center;
  color: white;
  margin-bottom: 40px;

  h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  p {
    font-size: 1.1rem;
    opacity: 0.9;
  }
}

.test-categories {
  max-width: 1200px;
  margin: 0 auto;
}

.category {
  margin-bottom: 40px;

  h2 {
    color: white;
    font-size: 1.5rem;
    margin-bottom: 20px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }
}

.test-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.test-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  .card-icon {
    font-size: 2.5rem;
    margin-bottom: 16px;
  }

  h3 {
    color: #333;
    font-size: 1.25rem;
    margin-bottom: 8px;
    font-weight: 600;
  }

  p {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 16px;
  }

  .card-tags {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;

    .tag {
      background: #f0f0f0;
      color: #555;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 0.75rem;
      font-weight: 500;
    }
  }
}

.footer {
  text-align: center;
  margin-top: 60px;

  p {
    color: white;
    opacity: 0.8;
    margin-bottom: 20px;
  }

  .back-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 12px 24px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
      border-color: rgba(255, 255, 255, 0.5);
    }
  }
}

@media (max-width: 768px) {
  .test-grid {
    grid-template-columns: 1fr;
  }

  .header h1 {
    font-size: 2rem;
  }

  .test-card {
    padding: 20px;
  }
}
</style>
