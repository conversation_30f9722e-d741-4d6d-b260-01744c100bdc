<template>
  <ZPage>
    <div class="categories-container">
      <!-- 导航栏区域，添加背景效果容器 -->
      <div class="nav-bar-effect">
        <!-- 自定义导航栏组件，传递确认事件、可见性绑定及搜索事件 -->
        <CustomNavbar
          ref="navbarRef"
          class="nav-bar"
          :confirm="handleConfirmFilters"
          :hasFilters="hasFilters"
          :searchValue="pageFilters.searchValue"
          :selectedProviders="pageFilters.selectedProviders"
          :providerDetails="pageFilters.providerDetails"
          v-model:visible="dialogVisible"
          @search="handleSearch"
          @confirm-filters="handleConfirmFilters"
          @clear-filters="handleClearFilters"
        />
      </div>
      <!-- 主要内容区域，分类展示 -->
      <div class="categories">
        <!-- 分类标签页，有分类数据时显示 -->
        <div class="categories-tabs">
          <van-tabs
            v-model:active="currentIndex"
            @change="handleTabChange"
            swipeable
            shrink
            line-height="0"
            background="transparent"
          >
            <van-tab
              v-for="category in filteredCategories"
              :key="category.id"
              :title="category.name"
            >
              <!-- 游戏列表容器，处理滚动事件和ref设置 -->
              <div
                class="games-container"
                @scroll="handleScroll($event, category.id)"
                :ref="(el) => setGamesContainerRef(el, category.id)"
              >
                <!-- 特殊分类（casino）的图片展示区域 -->
                <div v-show="isSpecialCategory(category.name)" class="special-category-images">
                  <Casino />
                </div>
                <!-- 游戏数据 -->
                <van-row
                  class="games-grid"
                  gutter="12"
                  v-if="category.pagedGames && category.pagedGames.length > 0"
                >
                  <van-col
                    v-for="game in category.pagedGames"
                    :key="game.id"
                    :span="getGameSpan(game, category)"
                  >
                    <!-- 游戏项组件，非换行标记时渲染，传递游戏数据和更新喜欢状态事件 -->
                    <GameItem
                      v-show="!isLineBreak(game)"
                      :game="game"
                      @updateLike="handleUpdateLike"
                    />
                  </van-col>
                </van-row>
                <!-- 无游戏数据状态展示 - 只有在不加载且确实无数据时显示 -->
                <div v-if="shouldShowNoData(category)" class="no-data">
                  <template v-if="hasFilters">
                    <div>
                      <ZNoData text="Your filters has returned no results"></ZNoData>
                      <div
                        v-if="hasFilters"
                        @click="handleClearFilters"
                        size="small"
                        class="clear-filters-btn"
                      >
                        Clear Filters
                      </div>
                    </div>
                  </template>
                  <template v-else>
                    <ZNoData text="No Record"></ZNoData>
                  </template>
                </div>
              </div>
            </van-tab>
          </van-tabs>
        </div>
      </div>
    </div>
  </ZPage>
</template>

<script setup lang="ts">
defineOptions({ name: "Categories" });
import { useRoute } from "vue-router";
import {
  ref,
  computed,
  reactive,
  onMounted,
  onActivated,
  onBeforeUnmount,
  watch,
  nextTick,
} from "vue";
import { storeToRefs } from "pinia";

import CustomNavbar from "./Components/CustomNavbar.vue";
import Casino from "./Components/Casino.vue";
import GameItem from "@/components/GameItem.vue";
import { useGameCategoriesStore } from "@/stores/gameCategories";
import { GameFiltersStorage } from "@/utils/managers/GameFiltersStorage";
import { useGameStore } from "@/stores/game";
import type { Game, GameCategory } from "./types";
import { showZLoading, closeZLoading } from "@/utils/ZLoadingAPI";

const route = useRoute();
const gameStore = useGameStore();
const { gameTypes } = storeToRefs(gameStore);

// 使用本地存储管理筛选状态
const pageFilters = reactive(GameFiltersStorage.getFilters());

// 存储每个分类的内容容器ref
const gamesContainers = ref<Record<string | number, HTMLElement | null>>({});

// 辅助函数：同步本地状态
const syncLocalState = () => {
  Object.assign(pageFilters, GameFiltersStorage.getFilters());
};

// 计算是否有筛选条件
const hasFilters = computed((): boolean => {
  return Boolean(
    pageFilters.selectedProviders.length > 0 && !pageFilters.selectedProviders.includes("all")
  );
});

const gameCategoriesStore = useGameCategoriesStore();
const { filteredCategories, isDataLoading, isLoadingMore, currentIndex, dialogVisible } =
  storeToRefs(gameCategoriesStore);

const navbarRef = ref();

const categoryId = computed(() => {
  return String(route.query.categoryId || gameTypes.value[0]?.id || "");
});

// 设置内容容器的ref
const setGamesContainerRef = (el: HTMLElement | null, categoryId: string | number) => {
  if (el) {
    gamesContainers.value[categoryId] = el;
  }
};

// 滚动到当前标签页顶部
const scrollToCurrentTabTop = () => {
  nextTick(() => {
    const currentCategory = filteredCategories.value[currentIndex.value];
    if (!currentCategory) return;

    const container = gamesContainers.value[currentCategory.id];
    if (container) {
      container.scrollTo({
        top: 0,
        behavior: "smooth",
      });
    }
  });
};

const handleClearFilters = async () => {
  showZLoading();

  try {
    GameFiltersStorage.clearFilters();
    syncLocalState();
    navbarRef.value?.setCheckedProviders();

    // 等待一小段时间确保状态更新完成
    await new Promise((resolve) => setTimeout(resolve, 100));

    scrollToCurrentTabTop(); // 触发滚动
  } finally {
    closeZLoading();
  }
};

const handleConfirmFilters = async (categories: string[] = [], providerDetails: any[] = []) => {
  showZLoading();

  try {
    GameFiltersStorage.setSelectedProviders(
      categories.length > 0 ? categories : ["all"],
      providerDetails
    );
    syncLocalState();

    // 等待一小段时间确保状态更新完成
    await new Promise((resolve) => setTimeout(resolve, 100));

    scrollToCurrentTabTop(); // 触发滚动
  } finally {
    closeZLoading();
  }
};

const handleSearch = async (value: string) => {
  // 只有在有搜索内容时才显示 loading
  if (value && value.trim()) {
    showZLoading();
  }

  try {
    GameFiltersStorage.setSearchValue(value);
    syncLocalState();

    // 等待一小段时间确保状态更新完成
    await new Promise((resolve) => setTimeout(resolve, 100));

    scrollToCurrentTabTop(); // 触发滚动
  } finally {
    // 只有在显示了 loading 时才关闭
    if (value && value.trim()) {
      closeZLoading();
    }
  }
};

const shouldShowNoData = (category: any) => {
  return !isDataLoading.value && (!category.pagedGames || !category.pagedGames.length);
};

const handleTabChange = async (index: number): Promise<void> => {
  await gameCategoriesStore.handleTabChange(index);
};

const handleScroll = async (event: Event, categoryId: string | number): Promise<void> => {
  const element = event.target as HTMLElement;
  const { scrollTop, scrollHeight, clientHeight } = element;
  if (scrollHeight - scrollTop - clientHeight < 50) {
    const category = filteredCategories.value.find((c) => c.id === categoryId);
    if (category?.hasMore && !isLoadingMore.value) {
      await gameCategoriesStore.loadMoreGames(categoryId);
    }
  }
};

const handleUpdateLike = async (updatedGame: Game): Promise<void> => {
  await gameCategoriesStore.handleUpdateLike(updatedGame);
};

const isSpecialCategory = (categoryName: string | number): boolean => {
  return String(categoryName).toLowerCase() === "casino";
};
const isLineBreak = (game: Game): boolean => {
  return String(game.id).startsWith("lineBreak_");
};
const getGameSpan = (game: Game, cate: GameCategory): number => {
  return game.big_images_set !== 0 && !["like", "history"].includes(String(cate.id)) ? 12 : 8;
};

const initializeTab = async () => {
  if (categoryId.value) {
    const index = filteredCategories.value.findIndex(
      (cat) => `${cat.id}` === `${categoryId.value}`
    );
    if (index !== -1) {
      await handleTabChange(index);
      return;
    }
  }
  if (filteredCategories.value.length > 0) {
    await handleTabChange(0);
  }
};

onMounted(async () => {
  // 首次进入时，从 URL 初始化筛选条件
  GameFiltersStorage.initFromQuery(route.query);
  syncLocalState();
  await initializeTab();
});

onActivated(() => {
  // 激活时刷新导航栏状态和本地状态
  syncLocalState();
  navbarRef.value?.forceRefresh?.();
});

// 页面离开时不需要清理，由路由守卫统一处理
onBeforeUnmount(() => {});

// 监听路由参数变化，处理 keep-alive 缓存组件的参数更新
watch(
  () => ({ categoryId: route.query.categoryId, providerIds: route.query.providerIds }),
  async (newQuery, oldQuery) => {
    // 检查是否有参数发生变化
    const categoryIdChanged = newQuery.categoryId !== oldQuery.categoryId;
    const providerIdsChanged = newQuery.providerIds !== oldQuery.providerIds;

    if (categoryIdChanged || providerIdsChanged) {
      // 如果 categoryId 发生变化，需要重新初始化 tab
      if (categoryIdChanged && newQuery.categoryId) {
        await initializeTab();
      }

      // 如果 providerIds 发生变化，需要更新筛选条件
      if (providerIdsChanged) {
        // 重新从 URL 初始化筛选条件
        GameFiltersStorage.initFromQuery(route.query);
        syncLocalState();
        // 刷新导航栏的筛选状态
        navbarRef.value?.forceRefresh?.();
      }
    }
  },
  { deep: true }
);
</script>

<style lang="scss" scoped>
// 防止页面滚动穿透
:global(body) {
  overflow: hidden;
}

:global(html) {
  overflow: hidden;
}
.categories-container {
  height: 100%; // 使用视口高度确保全屏
  overflow: hidden; // 防止页面级别的滚动
  position: relative;

  .nav-bar-effect {
    min-height: 60px;

    .nav-bar {
      // 导航栏固定顶部并添加磨砂玻璃效果
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      width: 100%;
      z-index: 1000; // 提高 z-index 确保在最顶层
    }
  }

  .categories {
    height: calc(100% - 60px);
    overflow: hidden;

    .categories-tabs {
      height: 100%;

      // 自定义 van-tabs 组件样式
      &:deep(.van-tabs) {
        height: 100%;

        .van-tabs__nav {
          // 优化 tab 导航栏样式，确保固定在顶部
          background: rgba(255, 255, 255, 0.98);
          backdrop-filter: blur(12px);
          position: sticky;
          top: 0;
          z-index: 100; // 提高 z-index
          width: 100%;
          // 确保不被遮挡
          transform: translateZ(0);
        }

        .van-tabs__content {
          height: calc(100% - 44px);
        }

        .van-tab__panel {
          height: 100%;
        }
      }

      .games-container {
        height: 100%;
        overflow-y: auto;
        overflow-x: hidden; // 防止水平滚动
        padding: 12px;
        -webkit-overflow-scrolling: touch;
        /* 关键属性，启用iOS弹性滚动 */
        // 确保滚动容器独立
        position: relative;
        // 防止滚动穿透
        overscroll-behavior: contain;

        .games-grid {
          /* 移除重复的滚动设置，避免双重滚动条 */

          &:deep(.game-item) {
            margin-bottom: 20px;

            .game-item-img {
              height: 110px !important;
            }

            .game-item-like {
              right: 6px;
              bottom: 6px;
            }
          }
        }

        // 自定义滚动条样式
        // &::-webkit-scrollbar {
        //   width: 0;
        // }

        // &::-webkit-scrollbar-track {
        //   background: transparent;
        // }

        &::-webkit-scrollbar-thumb {
          background: rgba(0, 0, 0, 0.1);
          border-radius: 2px;
        }

        &::-webkit-scrollbar-thumb:hover {
          background: rgba(0, 0, 0, 0.2);
        }
        // iOS 特定优化
        @supports (-webkit-overflow-scrolling: touch) {
          // 强制启用硬件加速
          -webkit-transform: translate3d(0, 0, 0);
          // 优化滚动回弹
          -webkit-overflow-scrolling: touch;
          // 减少滚动延迟
          -webkit-scroll-snap-type: none;
        }

        &::-webkit-scrollbar-thumb:hover {
          background: rgba(0, 0, 0, 0.2);
        }
      }

      // 特殊分类图片展示区域样式
      .special-category-images {
        margin-bottom: 12px;
      }

      // 无数据状态样式
      .no-data {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 20px 20px;
        text-align: center;

        .clear-filters-btn {
          margin-top: 16px;
          cursor: pointer;
          background-color: #eee;
          display: inline-block;
          padding: 6px 10px;
          border-radius: 20px;
        }
      }
    }
  }
}
</style>
