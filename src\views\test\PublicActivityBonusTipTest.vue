<template>
  <div class="test-page">
    <h1>PublicActivityBonusTip 组件测试</h1>
    
    <div class="test-controls">
      <h2>测试控制</h2>
      <div class="control-group">
        <label>显示弹窗:</label>
        <button @click="showDialog = !showDialog">
          {{ showDialog ? '隐藏' : '显示' }} 弹窗
        </button>
      </div>
      
      <div class="control-group">
        <label>提示文本:</label>
        <input v-model="tipsText" placeholder="输入提示文本" />
      </div>
      
      <div class="control-group">
        <label>奖励金额:</label>
        <input v-model="bonusAmount" placeholder="输入奖励金额" />
      </div>
      
      <div class="control-group">
        <label>日期文本:</label>
        <input v-model="dateText" placeholder="输入日期文本" />
      </div>
      
      <div class="control-group">
        <label>按钮文本:</label>
        <input v-model="buttonText" placeholder="输入按钮文本" />
      </div>
      
      <div class="control-group">
        <button @click="resetToDefaults">重置为默认值</button>
        <button @click="loadSampleData">加载示例数据</button>
      </div>
    </div>

    <div class="test-scenarios">
      <h2>测试场景</h2>
      <div class="scenario-buttons">
        <button @click="testScenario1">注册奖励</button>
        <button @click="testScenario2">反水奖励</button>
        <button @click="testScenario3">活动奖励</button>
        <button @click="testScenario4">大额奖励</button>
      </div>
    </div>

    <div class="event-log">
      <h2>事件日志</h2>
      <div class="log-content">
        <div v-for="(log, index) in eventLogs" :key="index" class="log-item">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-event">{{ log.event }}</span>
          <span class="log-data">{{ log.data }}</span>
        </div>
      </div>
      <button @click="clearLogs">清空日志</button>
    </div>

    <!-- 引入金币动画组件用于测试 -->
    <CoinAnimation 
      ref="coinAnimationRef" 
      :start-ref="startRef" 
      :end-ref="endRef"
      @animation-end="handleAnimationEnd" 
      @animation-start="handleAnimationStart" 
    />

    <!-- 目标位置 -->
    <div class="target-position" ref="endRef">
      <div class="coin-target">🪙 目标位置</div>
    </div>

    <!-- 测试组件 -->
    <PublicActivityBonusTip
      v-model:show="showDialog"
      :tips-text="tipsText"
      :bonus-amount="bonusAmount"
      :date-text="dateText"
      :button-text="buttonText"
      @click="handleClick"
      @start-coin-animation="handleStartCoinAnimation"
      @update:show="handleShowUpdate"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue';
import PublicActivityBonusTip from '@/components/ZPopDialog/PublicActivityBonusTip.vue';
import CoinAnimation from '@/components/CoinAnimation/index.vue';

// 响应式数据
const showDialog = ref(false);
const tipsText = ref('You received a bonus:');
const bonusAmount = ref('1000');
const dateText = ref('');
const buttonText = ref('Done');

// 事件日志
const eventLogs = ref<Array<{time: string, event: string, data: string}>>([]);

// 动画相关引用
const coinAnimationRef = ref();
const startRef = ref();
const endRef = ref();

// 添加日志
const addLog = (event: string, data: any = '') => {
  const time = new Date().toLocaleTimeString();
  eventLogs.value.unshift({
    time,
    event,
    data: typeof data === 'object' ? JSON.stringify(data) : String(data)
  });
  
  // 限制日志数量
  if (eventLogs.value.length > 50) {
    eventLogs.value = eventLogs.value.slice(0, 50);
  }
};

// 事件处理函数
const handleClick = () => {
  addLog('点击事件', '用户点击了确认按钮');
};

const handleStartCoinAnimation = (element: HTMLElement | null) => {
  addLog('开始金币动画', element ? '传入了DOM元素' : '未传入DOM元素');
  if (element && coinAnimationRef.value) {
    startRef.value = element;
    nextTick(() => {
      coinAnimationRef.value.startAnimation();
    });
  }
};

const handleShowUpdate = (value: boolean) => {
  addLog('显示状态更新', `show: ${value}`);
  showDialog.value = value;
};

const handleAnimationStart = () => {
  addLog('金币动画开始', '');
};

const handleAnimationEnd = () => {
  addLog('金币动画结束', '');
};

// 控制函数
const resetToDefaults = () => {
  tipsText.value = 'You received a bonus:';
  bonusAmount.value = '1000';
  dateText.value = '';
  buttonText.value = 'Done';
  addLog('重置为默认值', '');
};

const loadSampleData = () => {
  tipsText.value = 'You received a bonus in Welcome promotion:';
  bonusAmount.value = '5000';
  dateText.value = 'Reward date: 2024-01-15';
  buttonText.value = 'Claim Now';
  addLog('加载示例数据', '');
};

// 测试场景
const testScenario1 = () => {
  tipsText.value = 'You received a registration bonus:';
  bonusAmount.value = '100';
  dateText.value = 'Reward date: ' + new Date().toLocaleDateString();
  buttonText.value = 'Claim';
  showDialog.value = true;
  addLog('测试场景1', '注册奖励');
};

const testScenario2 = () => {
  tipsText.value = 'You received a cashback bonus:';
  bonusAmount.value = '250.50';
  dateText.value = 'Reward date: ' + new Date().toLocaleDateString();
  buttonText.value = 'Collect';
  showDialog.value = true;
  addLog('测试场景2', '反水奖励');
};

const testScenario3 = () => {
  tipsText.value = 'You received a bonus in Special Event promotion:';
  bonusAmount.value = '888';
  dateText.value = 'Reward date: ' + new Date().toLocaleDateString();
  buttonText.value = 'Get Bonus';
  showDialog.value = true;
  addLog('测试场景3', '活动奖励');
};

const testScenario4 = () => {
  tipsText.value = 'Congratulations! You won a jackpot bonus:';
  bonusAmount.value = '10000';
  dateText.value = 'Reward date: ' + new Date().toLocaleDateString();
  buttonText.value = 'Amazing!';
  showDialog.value = true;
  addLog('测试场景4', '大额奖励');
};

const clearLogs = () => {
  eventLogs.value = [];
  addLog('清空日志', '日志已清空');
};
</script>

<style lang="scss" scoped>
.test-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: 'Inter', sans-serif;
}

h1, h2 {
  color: #333;
  margin-bottom: 16px;
}

.test-controls {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  
  .control-group {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    gap: 12px;
    
    label {
      min-width: 100px;
      font-weight: 500;
    }
    
    input {
      flex: 1;
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
    }
    
    button {
      padding: 8px 16px;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      
      &:hover {
        background: #0056b3;
      }
    }
  }
}

.test-scenarios {
  background: #e8f4fd;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  
  .scenario-buttons {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    
    button {
      padding: 10px 20px;
      background: #28a745;
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      
      &:hover {
        background: #1e7e34;
      }
    }
  }
}

.event-log {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  
  .log-content {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 10px;
    background: white;
    margin-bottom: 12px;
    
    .log-item {
      display: flex;
      gap: 12px;
      padding: 4px 0;
      border-bottom: 1px solid #f1f3f4;
      font-size: 13px;
      
      &:last-child {
        border-bottom: none;
      }
      
      .log-time {
        color: #6c757d;
        min-width: 80px;
      }
      
      .log-event {
        color: #495057;
        min-width: 120px;
        font-weight: 500;
      }
      
      .log-data {
        color: #6f42c1;
        flex: 1;
      }
    }
  }
  
  button {
    padding: 8px 16px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    
    &:hover {
      background: #c82333;
    }
  }
}

.target-position {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1001;
  
  .coin-target {
    background: #ffd700;
    padding: 10px 15px;
    border-radius: 20px;
    font-weight: bold;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
  }
}
</style>
